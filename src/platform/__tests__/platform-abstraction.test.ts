import * as vscode from "vscode"
import { PlatformFactory, detectPlatformType } from "../PlatformFactory"
import { PlatformType } from "../interfaces"

describe("Platform Abstraction", () => {
  let mockContext: vscode.ExtensionContext

  beforeEach(() => {
    // Mock VS Code extension context
    mockContext = {
      subscriptions: [],
      workspaceState: {
        get: jest.fn(),
        update: jest.fn(),
        keys: jest.fn()
      },
      globalState: {
        get: jest.fn(),
        update: jest.fn(),
        keys: jest.fn(),
        setKeysForSync: jest.fn()
      },
      extensionPath: "/mock/extension/path",
      extensionUri: vscode.Uri.file("/mock/extension/path"),
      environmentVariableCollection: {} as any,
      extensionMode: vscode.ExtensionMode.Development,
      storageUri: vscode.Uri.file("/mock/storage"),
      globalStorageUri: vscode.Uri.file("/mock/global/storage"),
      logUri: vscode.Uri.file("/mock/log"),
      secrets: {
        get: jest.fn(),
        store: jest.fn(),
        delete: jest.fn(),
        onDidChange: jest.fn()
      },
      extension: {
        packageJSON: { version: "1.0.0" }
      } as any
    } as vscode.ExtensionContext
  })

  describe("Platform Detection", () => {
    it("should detect VS Code platform type", () => {
      const platformType = detectPlatformType()
      expect(platformType).toBe(PlatformType.VSCode)
    })
  })

  describe("Platform Factory", () => {
    it("should create VS Code platform services", async () => {
      const factory = PlatformFactory.getInstance()
      const services = await factory.createPlatformServices(PlatformType.VSCode, { context: mockContext })
      
      expect(services).toBeDefined()
      expect(services.platformType).toBe(PlatformType.VSCode)
      expect(services.fileSystem).toBeDefined()
      expect(services.webview).toBeDefined()
      expect(services.configuration).toBeDefined()
      expect(services.logger).toBeDefined()
      expect(services.workspace).toBeDefined()
    })

    it("should check platform support correctly", () => {
      const factory = PlatformFactory.getInstance()
      
      expect(factory.isPlatformSupported(PlatformType.VSCode)).toBe(true)
      expect(factory.isPlatformSupported(PlatformType.Standalone)).toBe(false)
      expect(factory.isPlatformSupported(PlatformType.Web)).toBe(false)
      expect(factory.isPlatformSupported(PlatformType.Server)).toBe(false)
    })

    it("should throw error for unsupported platforms", async () => {
      const factory = PlatformFactory.getInstance()
      
      await expect(
        factory.createPlatformServices(PlatformType.Standalone)
      ).rejects.toThrow("Standalone platform services not yet implemented")
    })
  })

  describe("VS Code Platform Services", () => {
    let services: any

    beforeEach(async () => {
      const factory = PlatformFactory.getInstance()
      services = await factory.createPlatformServices(PlatformType.VSCode, { context: mockContext })
    })

    afterEach(async () => {
      if (services) {
        await services.dispose()
      }
    })

    it("should provide file system operations", async () => {
      expect(services.fileSystem.readFile).toBeDefined()
      expect(services.fileSystem.writeFile).toBeDefined()
      expect(services.fileSystem.exists).toBeDefined()
      expect(services.fileSystem.listFiles).toBeDefined()
      expect(services.fileSystem.watchFiles).toBeDefined()
    })

    it("should provide configuration management", () => {
      expect(services.configuration.get).toBeDefined()
      expect(services.configuration.set).toBeDefined()
      expect(services.configuration.has).toBeDefined()
      expect(services.configuration.onDidChange).toBeDefined()
    })

    it("should provide logging capabilities", () => {
      const logger = services.logger.createLogger("test")
      
      expect(logger.info).toBeDefined()
      expect(logger.error).toBeDefined()
      expect(logger.warn).toBeDefined()
      expect(logger.debug).toBeDefined()
    })

    it("should provide workspace operations", () => {
      expect(services.workspace.getRootPath).toBeDefined()
      expect(services.workspace.getWorkspaceFolders).toBeDefined()
      expect(services.workspace.openFile).toBeDefined()
      expect(services.workspace.showNotification).toBeDefined()
    })

    it("should provide webview operations", () => {
      expect(services.webview.createWebview).toBeDefined()
      expect(services.webview.getActiveWebview).toBeDefined()
      expect(services.webview.onWebviewEvent).toBeDefined()
    })
  })

  describe("Configuration Abstraction", () => {
    let services: any

    beforeEach(async () => {
      const factory = PlatformFactory.getInstance()
      services = await factory.createPlatformServices(PlatformType.VSCode, { context: mockContext })
    })

    afterEach(async () => {
      if (services) {
        await services.dispose()
      }
    })

    it("should handle configuration get/set operations", async () => {
      const config = services.configuration
      
      // Mock VS Code configuration
      const mockGet = jest.fn().mockReturnValue("test-value")
      const mockUpdate = jest.fn().mockResolvedValue(undefined)
      
      // Mock vscode.workspace.getConfiguration
      const mockConfig = {
        get: mockGet,
        update: mockUpdate,
        inspect: jest.fn()
      }
      
      jest.spyOn(vscode.workspace, 'getConfiguration').mockReturnValue(mockConfig as any)
      
      // Test get operation
      const value = config.get("test.key", "default")
      expect(value).toBe("test-value")
      
      // Test set operation
      await config.set("test.key", "new-value")
      expect(mockUpdate).toHaveBeenCalledWith("test.key", "new-value", vscode.ConfigurationTarget.Global)
    })
  })
})
