import * as vscode from "vscode"
import { IConfiguration, ConfigurationScope, IDisposable } from "../interfaces/IConfiguration"

/**
 * VS Code implementation of the configuration interface
 * Wraps VS Code workspace configuration APIs
 */
export class VSCodeConfiguration implements IConfiguration {
  private changeListeners: Map<string, ((key: string, value: any, oldValue: any) => void)[]> = new Map()
  private disposables: vscode.Disposable[] = []

  constructor() {
    // Listen for configuration changes
    const configChangeDisposable = vscode.workspace.onDidChangeConfiguration((event) => {
      this.handleConfigurationChange(event)
    })
    this.disposables.push(configChangeDisposable)
  }

  get<T>(key: string, defaultValue?: T): T | undefined {
    const config = vscode.workspace.getConfiguration()
    return config.get<T>(key, defaultValue as T)
  }

  async set<T>(key: string, value: T, scope?: ConfigurationScope): Promise<void> {
    const config = vscode.workspace.getConfiguration()
    const target = this.mapScopeToConfigurationTarget(scope)
    
    await config.update(key, value, target)
  }

  has(key: string): boolean {
    const config = vscode.workspace.getConfiguration()
    const inspect = config.inspect(key)
    
    return inspect !== undefined && (
      inspect.globalValue !== undefined ||
      inspect.workspaceValue !== undefined ||
      inspect.workspaceFolderValue !== undefined ||
      inspect.defaultValue !== undefined
    )
  }

  async delete(key: string, scope?: ConfigurationScope): Promise<void> {
    const config = vscode.workspace.getConfiguration()
    const target = this.mapScopeToConfigurationTarget(scope)
    
    await config.update(key, undefined, target)
  }

  getAll(section?: string): Record<string, any> {
    const config = vscode.workspace.getConfiguration(section)
    const result: Record<string, any> = {}
    
    // Get all keys from the configuration
    const inspect = config.inspect('')
    if (inspect) {
      // Merge default, global, workspace, and workspace folder values
      Object.assign(result, inspect.defaultValue || {})
      Object.assign(result, inspect.globalValue || {})
      Object.assign(result, inspect.workspaceValue || {})
      Object.assign(result, inspect.workspaceFolderValue || {})
    }
    
    return result
  }

  onDidChange(
    key: string | undefined, 
    callback: (key: string, value: any, oldValue: any) => void
  ): IDisposable {
    const watchKey = key || '*'
    
    if (!this.changeListeners.has(watchKey)) {
      this.changeListeners.set(watchKey, [])
    }
    
    this.changeListeners.get(watchKey)!.push(callback)
    
    return {
      dispose: () => {
        const listeners = this.changeListeners.get(watchKey)
        if (listeners) {
          const index = listeners.indexOf(callback)
          if (index >= 0) {
            listeners.splice(index, 1)
          }
          
          if (listeners.length === 0) {
            this.changeListeners.delete(watchKey)
          }
        }
      }
    }
  }

  async reload(): Promise<void> {
    // VS Code automatically reloads configuration, so this is a no-op
    // In a standalone implementation, this would reload from file
  }

  async save(): Promise<void> {
    // VS Code automatically saves configuration, so this is a no-op
    // In a standalone implementation, this would save to file
  }

  private mapScopeToConfigurationTarget(scope?: ConfigurationScope): vscode.ConfigurationTarget {
    switch (scope) {
      case ConfigurationScope.Global:
        return vscode.ConfigurationTarget.Global
      case ConfigurationScope.Workspace:
        return vscode.ConfigurationTarget.Workspace
      case ConfigurationScope.User:
        return vscode.ConfigurationTarget.Global // VS Code doesn't distinguish user from global
      default:
        return vscode.ConfigurationTarget.Global
    }
  }

  private handleConfigurationChange(event: vscode.ConfigurationChangeEvent): void {
    // Notify listeners for specific keys
    for (const [watchKey, listeners] of this.changeListeners) {
      if (watchKey === '*') {
        // Global listeners get notified of all changes
        listeners.forEach(callback => {
          // We can't easily get the old value in VS Code, so pass undefined
          callback('*', undefined, undefined)
        })
      } else if (event.affectsConfiguration(watchKey)) {
        // Specific key listeners
        const newValue = this.get(watchKey)
        listeners.forEach(callback => {
          callback(watchKey, newValue, undefined)
        })
      }
    }
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose())
    this.disposables = []
    this.changeListeners.clear()
  }
}
